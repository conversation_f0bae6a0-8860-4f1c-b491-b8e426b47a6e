// 哪吒词云主脚本
class NezhaWordCloud {
    constructor() {
        this.svg = document.getElementById('wordcloudSvg');
        this.wordGroup = document.getElementById('wordGroup');
        this.currentLanguage = 'chinese';
        this.isAnimating = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.createWordCloud();
    }
    
    setupEventListeners() {
        // 语言切换
        document.getElementById('langToggle').addEventListener('click', () => {
            this.toggleLanguage();
        });
        
        // 重新动画
        document.getElementById('animateBtn').addEventListener('click', () => {
            this.animateWordCloud();
        });
        
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.debounce(this.updateLayout.bind(this), 300)();
        });
    }
    
    toggleLanguage() {
        if (this.isAnimating) return;
        
        this.currentLanguage = this.currentLanguage === 'chinese' ? 'english' : 'chinese';
        document.body.classList.toggle('english');
        
        this.createWordCloud();
    }
    
    createWordCloud() {
        this.clearWordCloud();
        
        const words = wordcloudData[this.currentLanguage];
        const positions = this.calculatePositions(words);
        
        this.renderWords(words, positions);
    }
    
    clearWordCloud() {
        this.wordGroup.innerHTML = '';
    }
    
    calculatePositions(words) {
        
        // 哪吒轮廓的近似边界
        const nezhaShape = {
            centerX: 400,
            centerY: 300,
            width: 200,
            height: 400
        };
        
        const positions = [];
        const placedWords = [];
        
        // 按字体大小排序，大字优先放置
        const sortedWords = [...words].sort((a, b) => b.size - a.size);
        
        for (let word of sortedWords) {
            let position = this.findValidPosition(word, nezhaShape, placedWords);
            if (position) {
                positions.push(position);
                placedWords.push({
                    ...position,
                    width: word.text.length * word.size * 0.6,
                    height: word.size
                });
            }
        }
        
        return positions;
    }
    
    findValidPosition(word, shape, placedWords) {
        const maxAttempts = 200;
        const fontSize = word.size;
        const textWidth = word.text.length * fontSize * 0.5;
        const textHeight = fontSize;

        // 定义哪吒形状的多个区域
        const regions = [
            // 头部区域
            { centerX: 400, centerY: 150, width: 120, height: 80, priority: 1 },
            // 身体上部
            { centerX: 400, centerY: 250, width: 100, height: 120, priority: 2 },
            // 身体中部
            { centerX: 400, centerY: 350, width: 140, height: 100, priority: 2 },
            // 身体下部
            { centerX: 400, centerY: 450, width: 120, height: 80, priority: 3 }
        ];

        // 根据词汇大小选择优先区域
        const suitableRegions = regions.filter(region => {
            if (fontSize >= 30) return region.priority <= 2; // 大词放在头部和上身
            if (fontSize >= 20) return region.priority <= 3; // 中词可以放在任何地方
            return true; // 小词可以放在任何地方
        });

        for (let attempt = 0; attempt < maxAttempts; attempt++) {
            // 随机选择一个合适的区域
            const region = suitableRegions[Math.floor(Math.random() * suitableRegions.length)];

            // 在选定区域内生成位置
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.random() * Math.min(region.width, region.height) * 0.4;

            const x = region.centerX + Math.cos(angle) * radius - textWidth / 2;
            const y = region.centerY + Math.sin(angle) * radius + textHeight / 4;

            // 检查是否在SVG边界内
            if (x < 30 || x + textWidth > 770 || y < 30 || y > 570) {
                continue;
            }

            // 检查是否与已放置的词重叠（减小间距要求）
            const padding = Math.max(5, fontSize * 0.1);
            const overlap = placedWords.some(placed => {
                return !(x + textWidth + padding < placed.x ||
                        x > placed.x + placed.width + padding ||
                        y + textHeight + padding < placed.y ||
                        y > placed.y + placed.height + padding);
            });

            if (!overlap) {
                return { x, y, word };
            }
        }

        // 如果找不到合适位置，使用螺旋算法
        return this.findSpiralPosition(word, shape, placedWords);
    }

    findSpiralPosition(word, shape, placedWords) {
        const fontSize = word.size;
        const textWidth = word.text.length * fontSize * 0.5;
        const textHeight = fontSize;

        let radius = 20;
        let angle = 0;
        const angleStep = 0.5;
        const radiusStep = 2;

        while (radius < 200) {
            const x = shape.centerX + Math.cos(angle) * radius - textWidth / 2;
            const y = shape.centerY + Math.sin(angle) * radius + textHeight / 4;

            if (x >= 30 && x + textWidth <= 770 && y >= 30 && y <= 570) {
                const padding = 3;
                const overlap = placedWords.some(placed => {
                    return !(x + textWidth + padding < placed.x ||
                            x > placed.x + placed.width + padding ||
                            y + textHeight + padding < placed.y ||
                            y > placed.y + placed.height + padding);
                });

                if (!overlap) {
                    return { x, y, word };
                }
            }

            angle += angleStep;
            if (angle > Math.PI * 2) {
                angle = 0;
                radius += radiusStep;
            }
        }

        // 最后的备用位置
        return {
            x: shape.centerX - textWidth / 2,
            y: shape.centerY + textHeight / 4,
            word
        };
    }
    
    renderWords(words, positions) {
        this.isAnimating = true;
        
        positions.forEach((pos, index) => {
            setTimeout(() => {
                const textElement = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                
                textElement.setAttribute('x', pos.x);
                textElement.setAttribute('y', pos.y);
                textElement.setAttribute('font-size', pos.word.size);
                textElement.setAttribute('fill', pos.word.color);
                textElement.setAttribute('class', 'word-text');
                textElement.textContent = pos.word.text;
                
                // 添加动画延迟
                textElement.style.animationDelay = `${index * 0.1}s`;
                
                // 添加悬停效果
                textElement.addEventListener('mouseenter', () => {
                    this.highlightCategory(pos.word.category);
                });
                
                textElement.addEventListener('mouseleave', () => {
                    this.clearHighlight();
                });
                
                // 添加点击效果
                textElement.addEventListener('click', () => {
                    this.showWordInfo(pos.word);
                });
                
                this.wordGroup.appendChild(textElement);
                
                // 最后一个词添加完成后，标记动画结束
                if (index === positions.length - 1) {
                    setTimeout(() => {
                        this.isAnimating = false;
                    }, 800);
                }
            }, index * 100);
        });
    }
    
    highlightCategory(category) {
        const words = this.wordGroup.querySelectorAll('.word-text');
        words.forEach(word => {
            const wordData = this.findWordData(word.textContent);
            word.classList.remove('highlighted', 'dimmed');

            if (wordData && wordData.category === category) {
                word.classList.add('highlighted');
            } else {
                word.classList.add('dimmed');
            }
        });
    }

    clearHighlight() {
        const words = this.wordGroup.querySelectorAll('.word-text');
        words.forEach(word => {
            word.classList.remove('highlighted', 'dimmed');
        });
    }
    
    findWordData(text) {
        return wordcloudData[this.currentLanguage].find(word => word.text === text);
    }
    
    showWordInfo(word) {
        // 创建临时提示框显示词汇信息
        const tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-size: 16px;
            z-index: 1000;
            backdrop-filter: blur(10px);
        `;
        
        const categoryNames = {
            character: this.currentLanguage === 'chinese' ? '人物' : 'Character',
            treasure: this.currentLanguage === 'chinese' ? '法宝' : 'Treasure',
            place: this.currentLanguage === 'chinese' ? '地点' : 'Place',
            action: this.currentLanguage === 'chinese' ? '动作' : 'Action',
            attribute: this.currentLanguage === 'chinese' ? '属性' : 'Attribute',
            magic: this.currentLanguage === 'chinese' ? '法术' : 'Magic',
            theme: this.currentLanguage === 'chinese' ? '主题' : 'Theme'
        };
        
        tooltip.innerHTML = `
            <strong>${word.text}</strong><br>
            ${this.currentLanguage === 'chinese' ? '类别' : 'Category'}: ${categoryNames[word.category]}<br>
            ${this.currentLanguage === 'chinese' ? '重要度' : 'Importance'}: ${word.size}
        `;
        
        document.body.appendChild(tooltip);
        
        setTimeout(() => {
            document.body.removeChild(tooltip);
        }, 2000);
    }
    
    animateWordCloud() {
        if (this.isAnimating) return;
        
        this.createWordCloud();
    }
    
    updateLayout() {
        // 响应式布局更新
        this.createWordCloud();
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new NezhaWordCloud();
});
