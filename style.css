/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', 'Roboto', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* 标题区域 */
.header {
    text-align: center;
    margin-bottom: 30px;
    animation: fadeInDown 1s ease-out;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.title-zh {
    color: #ff6b35;
    display: block;
    margin-bottom: 10px;
}

.title-en {
    color: #ffd700;
    font-size: 1.2rem;
    font-weight: 400;
    display: block;
}

/* 控制按钮 */
.controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.lang-toggle, .animate-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.lang-toggle:hover, .animate-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* 词云容器 */
.wordcloud-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 800px;
    animation: fadeInUp 1s ease-out 0.5s both;
}

.wordcloud-svg {
    width: 100%;
    height: auto;
    max-height: 600px;
    filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
}

/* 哪吒轮廓 */
.nezha-outline {
    fill: none;
    stroke: rgba(255, 255, 255, 0.4);
    stroke-width: 2;
    stroke-dasharray: 8,4;
    animation: dashMove 4s linear infinite;
    filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

/* 词云文字样式 */
.word-text {
    font-family: 'Noto Sans SC', 'Roboto', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    animation: wordFadeIn 0.8s ease-out forwards;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.word-text:hover {
    transform: scale(1.2);
    filter: brightness(1.3) drop-shadow(0 0 8px currentColor);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.word-text.highlighted {
    filter: brightness(1.5) drop-shadow(0 0 10px currentColor);
    animation: pulse 1.5s ease-in-out infinite;
}

.word-text.dimmed {
    opacity: 0.3;
    filter: grayscale(0.5);
}

/* 中英文切换 */
.lang-zh {
    display: inline;
}

.lang-en {
    display: none;
}

body.english .lang-zh {
    display: none;
}

body.english .lang-en {
    display: inline;
}

body.english .title-zh {
    display: none;
}

body.english .title-en {
    display: block;
    font-size: 2rem;
    color: #ff6b35;
}

/* 说明文字 */
.description {
    text-align: center;
    margin-top: 30px;
    animation: fadeInUp 1s ease-out 1s both;
}

.desc-zh, .desc-en {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    margin: 5px 0;
}

.desc-en {
    display: none;
}

body.english .desc-zh {
    display: none;
}

body.english .desc-en {
    display: block;
}

/* 动画定义 */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes wordFadeIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes dashMove {
    to {
        stroke-dashoffset: -10;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes glow {
    0%, 100% {
        filter: drop-shadow(0 0 5px currentColor);
    }
    50% {
        filter: drop-shadow(0 0 15px currentColor);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .title-en {
        font-size: 1rem;
    }
    
    body.english .title-en {
        font-size: 1.5rem;
    }
    
    .controls {
        gap: 10px;
    }
    
    .lang-toggle, .animate-btn {
        padding: 8px 16px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.5rem;
    }
    
    .wordcloud-svg {
        max-height: 400px;
    }
}
