<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>哪吒闹海 - 词云艺术</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 标题区域 -->
        <header class="header">
            <h1 class="title">
                <span class="title-zh">哪吒闹海</span>
                <span class="title-en"><PERSON><PERSON><PERSON> Conquers the Dragon King</span>
            </h1>
            <div class="controls">
                <button id="langToggle" class="lang-toggle">
                    <span class="lang-zh">中文</span>
                    <span class="lang-en">English</span>
                </button>
                <button id="animateBtn" class="animate-btn">重新动画</button>
            </div>
        </header>

        <!-- 词云容器 -->
        <main class="wordcloud-container">
            <svg id="wordcloudSvg" class="wordcloud-svg" viewBox="0 0 800 600">
                <!-- 哪吒轮廓路径 -->
                <defs>
                    <clipPath id="nezhaClip">
                        <!-- 更精细的哪吒人物轮廓 -->
                        <path id="nezhaPath" d="M400,80
                                             C385,75 375,85 370,100
                                             C365,115 370,130 375,145
                                             C380,160 385,175 390,190
                                             C395,205 400,220 405,235
                                             C410,250 415,265 420,280
                                             C425,295 430,310 435,325
                                             C440,340 445,355 450,370
                                             C455,385 460,400 465,415
                                             C470,430 475,445 480,460
                                             C485,475 490,490 485,505
                                             C480,520 470,530 455,535
                                             C440,540 425,535 410,530
                                             C395,525 380,520 365,515
                                             C350,510 335,505 325,495
                                             C315,485 310,470 315,455
                                             C320,440 330,425 340,410
                                             C350,395 360,380 370,365
                                             C380,350 390,335 395,320
                                             C400,305 395,290 390,275
                                             C385,260 380,245 375,230
                                             C370,215 365,200 360,185
                                             C355,170 350,155 355,140
                                             C360,125 370,110 380,100
                                             C390,90 395,85 400,80 Z

                                             M350,120 C360,115 370,120 375,130 C380,140 375,150 365,155 C355,160 345,155 340,145 C335,135 340,125 350,120 Z
                                             M425,120 C435,115 445,120 450,130 C455,140 450,150 440,155 C430,160 420,155 415,145 C410,135 415,125 425,120 Z"/>
                    </clipPath>
                    
                    <!-- 渐变定义 -->
                    <linearGradient id="fireGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#f7931e;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#ffd700;stop-opacity:1" />
                    </linearGradient>
                    
                    <linearGradient id="waterGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#3b82f6;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#60a5fa;stop-opacity:1" />
                    </linearGradient>
                </defs>
                
                <!-- 背景轮廓显示 -->
                <path id="nezhaOutline" class="nezha-outline"
                      d="M400,80 C385,75 375,85 370,100 C365,115 370,130 375,145 C380,160 385,175 390,190 C395,205 400,220 405,235 C410,250 415,265 420,280 C425,295 430,310 435,325 C440,340 445,355 450,370 C455,385 460,400 465,415 C470,430 475,445 480,460 C485,475 490,490 485,505 C480,520 470,530 455,535 C440,540 425,535 410,530 C395,525 380,520 365,515 C350,510 335,505 325,495 C315,485 310,470 315,455 C320,440 330,425 340,410 C350,395 360,380 370,365 C380,350 390,335 395,320 C400,305 395,290 390,275 C385,260 380,245 375,230 C370,215 365,200 360,185 C355,170 350,155 355,140 C360,125 370,110 380,100 C390,90 395,85 400,80 Z M350,120 C360,115 370,120 375,130 C380,140 375,150 365,155 C355,160 345,155 340,145 C335,135 340,125 350,120 Z M425,120 C435,115 445,120 450,130 C455,140 450,150 440,155 C430,160 420,155 415,145 C410,135 415,125 425,120 Z"/>
                
                <!-- 词云文字组 -->
                <g id="wordGroup" clip-path="url(#nezhaClip)">
                    <!-- 词云文字将通过JavaScript动态生成 -->
                </g>
            </svg>
        </main>

        <!-- 说明文字 -->
        <footer class="description">
            <p class="desc-zh">点击切换语言，体验哪吒闹海的传奇故事</p>
            <p class="desc-en">Click to switch language and experience the legend of Nezha</p>
        </footer>
    </div>

    <script src="wordcloud-data.js"></script>
    <script src="script.js"></script>
</body>
</html>
