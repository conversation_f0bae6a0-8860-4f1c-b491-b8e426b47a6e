// 哪吒闹海词云数据
const wordcloudData = {
    chinese: [
        // 主要人物
        { text: "哪吒", size: 48, color: "#ff6b35", category: "character" },
        { text: "龙王", size: 36, color: "#1e3a8a", category: "character" },
        { text: "太乙真人", size: 28, color: "#8b5cf6", category: "character" },
        { text: "李靖", size: 24, color: "#059669", category: "character" },
        { text: "龙太子", size: 22, color: "#0ea5e9", category: "character" },
        { text: "夜叉", size: 20, color: "#dc2626", category: "character" },
        
        // 神器法宝
        { text: "混天绫", size: 32, color: "#f59e0b", category: "treasure" },
        { text: "乾坤圈", size: 30, color: "#eab308", category: "treasure" },
        { text: "火尖枪", size: 26, color: "#ef4444", category: "treasure" },
        { text: "风火轮", size: 28, color: "#f97316", category: "treasure" },
        { text: "金砖", size: 18, color: "#fbbf24", category: "treasure" },
        { text: "七宝玲珑塔", size: 20, color: "#a855f7", category: "treasure" },
        
        // 地点场景
        { text: "东海", size: 34, color: "#0284c7", category: "place" },
        { text: "陈塘关", size: 24, color: "#16a34a", category: "place" },
        { text: "龙宫", size: 26, color: "#1d4ed8", category: "place" },
        { text: "天庭", size: 22, color: "#7c3aed", category: "place" },
        { text: "海底", size: 18, color: "#0891b2", category: "place" },
        
        // 动作情节
        { text: "闹海", size: 40, color: "#dc2626", category: "action" },
        { text: "降龙", size: 30, color: "#b91c1c", category: "action" },
        { text: "除妖", size: 24, color: "#991b1b", category: "action" },
        { text: "斗法", size: 22, color: "#7f1d1d", category: "action" },
        { text: "腾云", size: 18, color: "#6b7280", category: "action" },
        { text: "驾雾", size: 16, color: "#9ca3af", category: "action" },
        
        // 特征属性
        { text: "神通", size: 26, color: "#7c2d12", category: "attribute" },
        { text: "法力", size: 24, color: "#92400e", category: "attribute" },
        { text: "勇敢", size: 20, color: "#a16207", category: "attribute" },
        { text: "正义", size: 22, color: "#ca8a04", category: "attribute" },
        { text: "无畏", size: 18, color: "#eab308", category: "attribute" },
        { text: "神童", size: 16, color: "#facc15", category: "attribute" },
        
        // 神话元素
        { text: "仙术", size: 20, color: "#be185d", category: "magic" },
        { text: "变化", size: 18, color: "#c2410c", category: "magic" },
        { text: "三头六臂", size: 24, color: "#db2777", category: "magic" },
        { text: "莲花", size: 16, color: "#ec4899", category: "magic" },
        { text: "重生", size: 14, color: "#f472b6", category: "magic" },
        
        // 情感主题
        { text: "英雄", size: 28, color: "#b45309", category: "theme" },
        { text: "传奇", size: 20, color: "#d97706", category: "theme" },
        { text: "神话", size: 22, color: "#f59e0b", category: "theme" },
        { text: "童年", size: 16, color: "#fbbf24", category: "theme" },
        { text: "成长", size: 18, color: "#fcd34d", category: "theme" },

        // 补充词汇
        { text: "小英雄", size: 20, color: "#dc2626", category: "character" },
        { text: "龙族", size: 18, color: "#1e40af", category: "character" },
        { text: "仙人", size: 16, color: "#7c3aed", category: "character" },
        { text: "海神", size: 14, color: "#0891b2", category: "character" },
        { text: "神兵", size: 16, color: "#f59e0b", category: "treasure" },
        { text: "宝物", size: 14, color: "#eab308", category: "treasure" },
        { text: "神器", size: 18, color: "#fbbf24", category: "treasure" },
        { text: "战斗", size: 20, color: "#dc2626", category: "action" },
        { text: "拯救", size: 18, color: "#059669", category: "action" },
        { text: "保护", size: 16, color: "#16a34a", category: "action" },
        { text: "威武", size: 14, color: "#ca8a04", category: "attribute" },
        { text: "机智", size: 16, color: "#a16207", category: "attribute" },
        { text: "坚强", size: 14, color: "#92400e", category: "attribute" }
    ],
    
    english: [
        // Main Characters
        { text: "Nezha", size: 48, color: "#ff6b35", category: "character" },
        { text: "Dragon King", size: 36, color: "#1e3a8a", category: "character" },
        { text: "Taiyi", size: 28, color: "#8b5cf6", category: "character" },
        { text: "Li Jing", size: 24, color: "#059669", category: "character" },
        { text: "Dragon Prince", size: 22, color: "#0ea5e9", category: "character" },
        { text: "Yaksha", size: 20, color: "#dc2626", category: "character" },
        
        // Magical Treasures
        { text: "Huntian Ling", size: 32, color: "#f59e0b", category: "treasure" },
        { text: "Universe Ring", size: 30, color: "#eab308", category: "treasure" },
        { text: "Fire Spear", size: 26, color: "#ef4444", category: "treasure" },
        { text: "Wind Fire Wheels", size: 28, color: "#f97316", category: "treasure" },
        { text: "Golden Brick", size: 18, color: "#fbbf24", category: "treasure" },
        { text: "Pagoda Tower", size: 20, color: "#a855f7", category: "treasure" },
        
        // Places
        { text: "East Sea", size: 34, color: "#0284c7", category: "place" },
        { text: "Chentang Pass", size: 24, color: "#16a34a", category: "place" },
        { text: "Dragon Palace", size: 26, color: "#1d4ed8", category: "place" },
        { text: "Heaven", size: 22, color: "#7c3aed", category: "place" },
        { text: "Seabed", size: 18, color: "#0891b2", category: "place" },
        
        // Actions
        { text: "Conquer", size: 40, color: "#dc2626", category: "action" },
        { text: "Subdue Dragon", size: 30, color: "#b91c1c", category: "action" },
        { text: "Defeat Demons", size: 24, color: "#991b1b", category: "action" },
        { text: "Magic Battle", size: 22, color: "#7f1d1d", category: "action" },
        { text: "Fly", size: 18, color: "#6b7280", category: "action" },
        { text: "Soar", size: 16, color: "#9ca3af", category: "action" },
        
        // Attributes
        { text: "Divine Power", size: 26, color: "#7c2d12", category: "attribute" },
        { text: "Magic", size: 24, color: "#92400e", category: "attribute" },
        { text: "Brave", size: 20, color: "#a16207", category: "attribute" },
        { text: "Justice", size: 22, color: "#ca8a04", category: "attribute" },
        { text: "Fearless", size: 18, color: "#eab308", category: "attribute" },
        { text: "Prodigy", size: 16, color: "#facc15", category: "attribute" },
        
        // Magical Elements
        { text: "Immortal Arts", size: 20, color: "#be185d", category: "magic" },
        { text: "Transform", size: 18, color: "#c2410c", category: "magic" },
        { text: "Three Heads Six Arms", size: 24, color: "#db2777", category: "magic" },
        { text: "Lotus", size: 16, color: "#ec4899", category: "magic" },
        { text: "Rebirth", size: 14, color: "#f472b6", category: "magic" },
        
        // Themes
        { text: "Hero", size: 28, color: "#b45309", category: "theme" },
        { text: "Legend", size: 20, color: "#d97706", category: "theme" },
        { text: "Mythology", size: 22, color: "#f59e0b", category: "theme" },
        { text: "Childhood", size: 16, color: "#fbbf24", category: "theme" },
        { text: "Growth", size: 18, color: "#fcd34d", category: "theme" },

        // Additional Words
        { text: "Little Hero", size: 20, color: "#dc2626", category: "character" },
        { text: "Dragon Clan", size: 18, color: "#1e40af", category: "character" },
        { text: "Immortal", size: 16, color: "#7c3aed", category: "character" },
        { text: "Sea God", size: 14, color: "#0891b2", category: "character" },
        { text: "Divine Weapon", size: 16, color: "#f59e0b", category: "treasure" },
        { text: "Artifact", size: 14, color: "#eab308", category: "treasure" },
        { text: "Sacred Item", size: 18, color: "#fbbf24", category: "treasure" },
        { text: "Battle", size: 20, color: "#dc2626", category: "action" },
        { text: "Rescue", size: 18, color: "#059669", category: "action" },
        { text: "Protect", size: 16, color: "#16a34a", category: "action" },
        { text: "Mighty", size: 14, color: "#ca8a04", category: "attribute" },
        { text: "Clever", size: 16, color: "#a16207", category: "attribute" },
        { text: "Strong", size: 14, color: "#92400e", category: "attribute" }
    ]
};

// 词汇类别颜色映射
const categoryColors = {
    character: ["#ff6b35", "#dc2626", "#059669"],
    treasure: ["#f59e0b", "#eab308", "#fbbf24"],
    place: ["#0284c7", "#1d4ed8", "#0891b2"],
    action: ["#dc2626", "#b91c1c", "#991b1b"],
    attribute: ["#7c2d12", "#92400e", "#a16207"],
    magic: ["#be185d", "#db2777", "#ec4899"],
    theme: ["#b45309", "#d97706", "#f59e0b"]
};
