# 哪吒闹海词云艺术 | <PERSON><PERSON><PERSON> Conquers the Dragon King Word Cloud

一个以哪吒形象为轮廓的交互式词云网页，展现中国古典神话故事《哪吒闹海》的精彩内容。

An interactive word cloud webpage featuring the silhouette of <PERSON><PERSON><PERSON>, showcasing the classic Chinese mythological story "<PERSON><PERSON><PERSON> Conquers the Dragon King".

## 🌟 特色功能 | Features

### 🎨 视觉效果 | Visual Effects
- **哪吒轮廓设计** - 精心设计的哪吒人物SVG轮廓
- **渐变色彩** - 火焰与海水主题的渐变背景
- **动态动画** - 词汇逐个出现的流畅动画效果
- **悬停特效** - 鼠标悬停时的高亮和缩放效果

### 🌐 双语支持 | Bilingual Support
- **中文版本** - 包含哪吒、龙王、混天绫等经典词汇
- **英文版本** - 对应的英文翻译词汇
- **一键切换** - 点击按钮即可切换语言

### 🎯 交互功能 | Interactive Features
- **类别高亮** - 悬停词汇时同类别词汇会高亮显示
- **词汇信息** - 点击词汇显示详细信息
- **重新动画** - 点击按钮重新播放词云动画
- **响应式设计** - 适配不同屏幕尺寸

## 📁 文件结构 | File Structure

```
├── index.html          # 主页面文件
├── style.css           # 样式文件
├── script.js           # 主要JavaScript逻辑
├── wordcloud-data.js   # 词汇数据文件
└── README.md           # 说明文档
```

## 🚀 使用方法 | Usage

### 本地运行 | Local Development

1. **克隆或下载项目文件**
   ```bash
   # 确保所有文件在同一目录下
   ```

2. **启动本地服务器**
   ```bash
   # 使用Python
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   
   # 或使用任何其他静态文件服务器
   ```

3. **打开浏览器访问**
   ```
   http://localhost:8000
   ```

### 直接打开 | Direct Access
也可以直接双击 `index.html` 文件在浏览器中打开，但建议使用本地服务器以获得最佳体验。

## 🎮 操作指南 | User Guide

### 基本操作 | Basic Operations
- **语言切换** - 点击"中文/English"按钮切换语言
- **重新动画** - 点击"重新动画"按钮重播词云动画
- **词汇交互** - 鼠标悬停在词汇上查看同类别高亮效果
- **详细信息** - 点击词汇查看类别和重要度信息

### 词汇分类 | Word Categories
- **人物 (Characters)** - 哪吒、龙王、太乙真人等
- **法宝 (Treasures)** - 混天绫、乾坤圈、火尖枪等
- **地点 (Places)** - 东海、陈塘关、龙宫等
- **动作 (Actions)** - 闹海、降龙、除妖等
- **属性 (Attributes)** - 神通、法力、勇敢等
- **法术 (Magic)** - 仙术、变化、三头六臂等
- **主题 (Themes)** - 英雄、传奇、神话等

## 🛠️ 技术实现 | Technical Implementation

### 核心技术 | Core Technologies
- **HTML5 & SVG** - 结构和矢量图形
- **CSS3** - 样式、动画和响应式设计
- **Vanilla JavaScript** - 交互逻辑和动画控制

### 关键算法 | Key Algorithms
- **词云布局算法** - 智能避免词汇重叠的位置计算
- **轮廓检测** - SVG路径内的点位置验证
- **动画序列** - 渐进式词汇显示动画

### 设计特色 | Design Features
- **中国风配色** - 火焰橙红与海水蓝的经典搭配
- **传统与现代结合** - 古典神话与现代网页技术的融合
- **用户体验优化** - 流畅的动画和直观的交互设计

## 🎨 自定义 | Customization

### 修改词汇数据 | Modify Word Data
编辑 `wordcloud-data.js` 文件中的 `wordcloudData` 对象来添加、删除或修改词汇。

### 调整视觉效果 | Adjust Visual Effects
在 `style.css` 文件中修改颜色、字体、动画等样式属性。

### 更改轮廓形状 | Change Silhouette Shape
在 `index.html` 文件中修改 SVG 路径来改变词云的形状轮廓。

## 📱 兼容性 | Compatibility

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ 移动端浏览器

## 🤝 贡献 | Contributing

欢迎提交问题报告、功能建议或代码贡献！

Welcome to submit issues, feature requests, or code contributions!

## 📄 许可证 | License

本项目采用 MIT 许可证。

This project is licensed under the MIT License.

---

**享受哪吒闹海的传奇故事！ | Enjoy the legendary tale of Nezha!** 🐉⚡
